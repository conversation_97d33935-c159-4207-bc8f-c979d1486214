"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/students/page",{

/***/ "(app-pages-browser)/./app/students/page.tsx":
/*!*******************************!*\
  !*** ./app/students/page.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StudentsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_loading__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/loading */ \"(app-pages-browser)/./components/ui/loading.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevrons-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,ChevronsLeft,ChevronsRight,Edit,Eye,Mail,MoreHorizontal,Plus,QrCode,Search,Trash2,Upload,UserCheck,UserPlus,UserX,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevrons-right.js\");\n/* harmony import */ var _components_students_student_form_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/students/student-form-modal */ \"(app-pages-browser)/./components/students/student-form-modal.tsx\");\n/* harmony import */ var _components_students_student_profile_modal__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/students/student-profile-modal */ \"(app-pages-browser)/./components/students/student-profile-modal.tsx\");\n/* harmony import */ var _components_students_bulk_actions_modal__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/students/bulk-actions-modal */ \"(app-pages-browser)/./components/students/bulk-actions-modal.tsx\");\n/* harmony import */ var _components_students_import_export_modal__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/students/import-export-modal */ \"(app-pages-browser)/./components/students/import-export-modal.tsx\");\n/* harmony import */ var _components_students_delete_confirmation_dialog__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/students/delete-confirmation-dialog */ \"(app-pages-browser)/./components/students/delete-confirmation-dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Mock data for demonstration\nconst mockStudents = [\n    {\n        id: \"1\",\n        studentId: \"TSAT-2024-001\",\n        firstName: \"John\",\n        lastName: \"Doe\",\n        middleName: \"Smith\",\n        email: \"<EMAIL>\",\n        phone: \"+63 ************\",\n        dateOfBirth: new Date(\"2005-03-15\"),\n        address: \"123 Main St, Tanauan City\",\n        course: \"Information Technology\",\n        yearLevel: 2,\n        section: \"IT-2A\",\n        status: \"active\",\n        qrCode: \"QR001\",\n        avatar: \"/avatars/john-doe.jpg\",\n        guardianName: \"Jane Doe\",\n        guardianPhone: \"+63 ************\",\n        enrollmentDate: new Date(\"2023-08-15\"),\n        createdAt: new Date(\"2023-08-15\"),\n        updatedAt: new Date(\"2024-01-15\")\n    },\n    {\n        id: \"2\",\n        studentId: \"TSAT-2024-002\",\n        firstName: \"Maria\",\n        lastName: \"Santos\",\n        email: \"<EMAIL>\",\n        phone: \"+63 ************\",\n        dateOfBirth: new Date(\"2004-07-22\"),\n        address: \"456 Oak Ave, Tanauan City\",\n        course: \"Business Administration\",\n        yearLevel: 3,\n        section: \"BA-3B\",\n        status: \"active\",\n        qrCode: \"QR002\",\n        guardianName: \"Roberto Santos\",\n        guardianPhone: \"+63 ************\",\n        enrollmentDate: new Date(\"2022-08-15\"),\n        createdAt: new Date(\"2022-08-15\"),\n        updatedAt: new Date(\"2024-01-10\")\n    },\n    {\n        id: \"3\",\n        studentId: \"TSAT-2024-003\",\n        firstName: \"Carlos\",\n        lastName: \"Rodriguez\",\n        middleName: \"Miguel\",\n        email: \"<EMAIL>\",\n        phone: \"+63 ************\",\n        dateOfBirth: new Date(\"2005-11-08\"),\n        address: \"789 Pine St, Tanauan City\",\n        course: \"Automotive Technology\",\n        yearLevel: 1,\n        section: \"AT-1A\",\n        status: \"inactive\",\n        qrCode: \"QR003\",\n        guardianName: \"Elena Rodriguez\",\n        guardianPhone: \"+63 ************\",\n        enrollmentDate: new Date(\"2024-08-15\"),\n        createdAt: new Date(\"2024-08-15\"),\n        updatedAt: new Date(\"2024-01-20\")\n    }\n];\nconst mockStats = {\n    total: 1250,\n    active: 1180,\n    inactive: 45,\n    newEnrollments: 25,\n    graduated: 320,\n    dropped: 15\n};\nfunction StudentsPage() {\n    var _filters_yearLevel;\n    _s();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockStudents);\n    const [selectedStudents, setSelectedStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sortConfig, setSortConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        field: \"lastName\",\n        direction: \"asc\"\n    });\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Modal states\n    const [isAddModalOpen, setIsAddModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProfileModalOpen, setIsProfileModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkActionsModalOpen, setIsBulkActionsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isImportExportModalOpen, setIsImportExportModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedStudentProfile, setSelectedStudentProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort students\n    const filteredAndSortedStudents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let filtered = students;\n        // Apply filters\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            filtered = filtered.filter((student)=>{\n                var _student_email;\n                return student.firstName.toLowerCase().includes(searchLower) || student.lastName.toLowerCase().includes(searchLower) || student.studentId.toLowerCase().includes(searchLower) || ((_student_email = student.email) === null || _student_email === void 0 ? void 0 : _student_email.toLowerCase().includes(searchLower));\n            });\n        }\n        if (filters.status) {\n            filtered = filtered.filter((student)=>student.status === filters.status);\n        }\n        if (filters.course) {\n            filtered = filtered.filter((student)=>student.course === filters.course);\n        }\n        if (filters.yearLevel) {\n            filtered = filtered.filter((student)=>student.yearLevel === filters.yearLevel);\n        }\n        // Apply sorting\n        filtered.sort((a, b)=>{\n            const aValue = a[sortConfig.field];\n            const bValue = b[sortConfig.field];\n            if (aValue < bValue) return sortConfig.direction === \"asc\" ? -1 : 1;\n            if (aValue > bValue) return sortConfig.direction === \"asc\" ? 1 : -1;\n            return 0;\n        });\n        return filtered;\n    }, [\n        students,\n        filters,\n        sortConfig\n    ]);\n    // Pagination\n    const paginatedStudents = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const startIndex = (currentPage - 1) * pageSize;\n        return filteredAndSortedStudents.slice(startIndex, startIndex + pageSize);\n    }, [\n        filteredAndSortedStudents,\n        currentPage,\n        pageSize\n    ]);\n    const totalPages = Math.ceil(filteredAndSortedStudents.length / pageSize);\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedStudents(paginatedStudents.map((student)=>student.id));\n        } else {\n            setSelectedStudents([]);\n        }\n    };\n    const handleSelectStudent = (studentId, checked)=>{\n        if (checked) {\n            setSelectedStudents((prev)=>[\n                    ...prev,\n                    studentId\n                ]);\n        } else {\n            setSelectedStudents((prev)=>prev.filter((id)=>id !== studentId));\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const variants = {\n            active: \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\",\n            inactive: \"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300\",\n            graduated: \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\",\n            dropped: \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n            className: variants[status],\n            children: status.charAt(0).toUpperCase() + status.slice(1)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this);\n    };\n    const handleAddStudent = async (data)=>{\n        try {\n            setError(null);\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const newStudent = {\n                id: Date.now().toString(),\n                ...data,\n                dateOfBirth: new Date(data.dateOfBirth),\n                status: \"active\",\n                qrCode: \"QR\".concat(Date.now()),\n                enrollmentDate: new Date(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n            };\n            setStudents((prev)=>[\n                    ...prev,\n                    newStudent\n                ]);\n        } catch (error) {\n            setError(\"Failed to add student. Please try again.\");\n            throw error;\n        }\n    };\n    const handleEditStudent = async (data)=>{\n        if (!selectedStudent) return;\n        try {\n            setError(null);\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const updatedStudent = {\n                ...selectedStudent,\n                ...data,\n                dateOfBirth: new Date(data.dateOfBirth),\n                updatedAt: new Date()\n            };\n            setStudents((prev)=>prev.map((s)=>s.id === selectedStudent.id ? updatedStudent : s));\n            setSelectedStudent(null);\n        } catch (error) {\n            setError(\"Failed to update student. Please try again.\");\n            throw error;\n        }\n    };\n    const handleViewProfile = (student)=>{\n        // Convert Student to EnhancedStudent for profile view\n        const enhancedStudent = {\n            ...student,\n            fullName: \"\".concat(student.firstName, \" \").concat(student.middleName ? student.middleName + \" \" : \"\").concat(student.lastName),\n            guardians: student.guardianName ? [\n                {\n                    name: student.guardianName,\n                    relationship: \"Parent/Guardian\",\n                    phone: student.guardianPhone || \"\",\n                    emergencyContact: true\n                }\n            ] : [],\n            academicRecords: [],\n            attendanceSummary: {\n                totalDays: 100,\n                presentDays: 85,\n                absentDays: 10,\n                lateDays: 5,\n                excusedDays: 0,\n                attendanceRate: 85\n            },\n            smsNotifications: [],\n            qrCodeData: {\n                studentId: student.id,\n                generatedAt: student.createdAt,\n                isActive: true,\n                scanCount: 0\n            },\n            emergencyContacts: []\n        };\n        setSelectedStudentProfile(enhancedStudent);\n        setIsProfileModalOpen(true);\n    };\n    const handleEditClick = (student)=>{\n        setSelectedStudent(student);\n        setIsEditModalOpen(true);\n    };\n    const handleDeleteClick = (student)=>{\n        setSelectedStudent(student);\n        setIsDeleteDialogOpen(true);\n    };\n    const handleDeleteConfirm = async (studentId)=>{\n        try {\n            setIsDeleting(true);\n            setError(null);\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Optimistic update\n            setStudents((prev)=>prev.filter((s)=>s.id !== studentId));\n            setSelectedStudents((prev)=>prev.filter((id)=>id !== studentId));\n        } catch (error) {\n            setError(\"Failed to delete student. Please try again.\");\n            throw error;\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: \"Students\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Student Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Manage student records, attendance, and academic information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setIsImportExportModalOpen(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Import/Export\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    size: \"sm\",\n                                    onClick: ()=>setIsAddModalOpen(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: mockStats.total.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"+\",\n                                                mockStats.newEnrollments,\n                                                \" from last month\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Active Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: mockStats.active.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                (mockStats.active / mockStats.total * 100).toFixed(1),\n                                                \"% of total\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Inactive Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: mockStats.inactive\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                (mockStats.inactive / mockStats.total * 100).toFixed(1),\n                                                \"% of total\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"New Enrollments\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: mockStats.newEnrollments\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"This month\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"Student Directory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"Search and filter student records\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        placeholder: \"Search students...\",\n                                                        value: filters.search || \"\",\n                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    search: e.target.value\n                                                                })),\n                                                        className: \"pl-10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                    value: filters.status || \"all\",\n                                                    onValueChange: (value)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                status: value === \"all\" ? undefined : value\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                            className: \"w-32\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                                placeholder: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"all\",\n                                                                    children: \"All Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"active\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"inactive\",\n                                                                    children: \"Inactive\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"graduated\",\n                                                                    children: \"Graduated\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"dropped\",\n                                                                    children: \"Dropped\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                    value: filters.course || \"all\",\n                                                    onValueChange: (value)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                course: value === \"all\" ? undefined : value\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                            className: \"w-48\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                                placeholder: \"Course\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"all\",\n                                                                    children: \"All Courses\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"Information Technology\",\n                                                                    children: \"Information Technology\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"Business Administration\",\n                                                                    children: \"Business Administration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"Automotive Technology\",\n                                                                    children: \"Automotive Technology\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                    value: ((_filters_yearLevel = filters.yearLevel) === null || _filters_yearLevel === void 0 ? void 0 : _filters_yearLevel.toString()) || \"all\",\n                                                    onValueChange: (value)=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                yearLevel: value === \"all\" ? undefined : parseInt(value)\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                            className: \"w-32\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                                placeholder: \"Year\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"all\",\n                                                                    children: \"All Years\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"1\",\n                                                                    children: \"Year 1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"2\",\n                                                                    children: \"Year 2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"3\",\n                                                                    children: \"Year 3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: \"4\",\n                                                                    children: \"Year 4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                selectedStudents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: [\n                                                selectedStudents.length,\n                                                \" student\",\n                                                selectedStudents.length > 1 ? \"s\" : \"\",\n                                                \" selected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 ml-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setIsBulkActionsModalOpen(true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Bulk Actions\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.Alert, {\n                                    className: \"border-red-200 bg-red-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertCircle, {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_9__.AlertDescription, {\n                                            className: \"text-red-800\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded-md border\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading__WEBPACK_IMPORTED_MODULE_10__.LoadingTable, {\n                                            rows: pageSize,\n                                            columns: 8\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            className: \"w-12\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                                checked: selectedStudents.length === paginatedStudents.length && paginatedStudents.length > 0,\n                                                                onCheckedChange: handleSelectAll\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Photo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Student ID\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Grade & Section\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            className: \"text-right\",\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n                                                children: paginatedStudents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        colSpan: 8,\n                                                        className: \"text-center py-8 text-muted-foreground\",\n                                                        children: \"No students found. Try adjusting your filters or add new students.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 23\n                                                }, this) : paginatedStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                                    checked: selectedStudents.includes(student.id),\n                                                                    onCheckedChange: (checked)=>handleSelectStudent(student.id, checked)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                                                    className: \"h-10 w-10\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                                                            src: student.avatar,\n                                                                            alt: \"\".concat(student.firstName, \" \").concat(student.lastName)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                                                            children: [\n                                                                                student.firstName.charAt(0),\n                                                                                student.lastName.charAt(0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            student.firstName,\n                                                                            \" \",\n                                                                            student.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    student.middleName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: student.middleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                className: \"font-mono text-sm\",\n                                                                children: student.studentId\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium\",\n                                                                            children: student.course\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: [\n                                                                                \"Year \",\n                                                                                student.yearLevel,\n                                                                                \" - \",\n                                                                                student.section\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        student.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: student.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        student.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: student.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: getStatusBadge(student.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenu, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                className: \"h-8 w-8 p-0\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"sr-only\",\n                                                                                        children: \"Open menu\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                        lineNumber: 608,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                        lineNumber: 609,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                lineNumber: 607,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuContent, {\n                                                                            align: \"end\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuLabel, {\n                                                                                    children: \"Actions\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                    lineNumber: 613,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleViewProfile(student),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                            lineNumber: 615,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"View Profile\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                    lineNumber: 614,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                                                    onClick: ()=>handleEditClick(student),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                            lineNumber: 619,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Edit Student\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                    lineNumber: 618,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                            lineNumber: 623,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Generate QR\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                    lineNumber: 622,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                            lineNumber: 627,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Send SMS\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                    lineNumber: 626,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                    lineNumber: 630,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_13__.DropdownMenuItem, {\n                                                                                    className: \"text-red-600\",\n                                                                                    onClick: ()=>handleDeleteClick(student),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                            lineNumber: 635,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Delete Student\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                                    lineNumber: 631,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, student.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between px-2 py-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Rows per page\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                    value: pageSize.toString(),\n                                                    onValueChange: (value)=>{\n                                                        setPageSize(parseInt(value));\n                                                        setCurrentPage(1);\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                            className: \"h-8 w-[70px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                lineNumber: 661,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                            side: \"top\",\n                                                            children: [\n                                                                5,\n                                                                10,\n                                                                20,\n                                                                30,\n                                                                40,\n                                                                50\n                                                            ].map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                    value: size.toString(),\n                                                                    children: size\n                                                                }, size, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-6 lg:space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex w-[100px] items-center justify-center text-sm font-medium\",\n                                                    children: [\n                                                        \"Page \",\n                                                        currentPage,\n                                                        \" of \",\n                                                        totalPages\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"hidden h-8 w-8 p-0 lg:flex\",\n                                                            onClick: ()=>setCurrentPage(1),\n                                                            disabled: currentPage === 1,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Go to first page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"h-8 w-8 p-0\",\n                                                            onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                                            disabled: currentPage === 1,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Go to previous page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"h-8 w-8 p-0\",\n                                                            onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                                            disabled: currentPage === totalPages,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Go to next page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"hidden h-8 w-8 p-0 lg:flex\",\n                                                            onClick: ()=>setCurrentPage(totalPages),\n                                                            disabled: currentPage === totalPages,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"sr-only\",\n                                                                    children: \"Go to last page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 710,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_ChevronsLeft_ChevronsRight_Edit_Eye_Mail_MoreHorizontal_Plus_QrCode_Search_Trash2_Upload_UserCheck_UserPlus_UserX_Users_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_students_student_form_modal__WEBPACK_IMPORTED_MODULE_14__.StudentFormModal, {\n                    open: isAddModalOpen,\n                    onOpenChange: setIsAddModalOpen,\n                    onSubmit: handleAddStudent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                    lineNumber: 720,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_students_student_form_modal__WEBPACK_IMPORTED_MODULE_14__.StudentFormModal, {\n                    open: isEditModalOpen,\n                    onOpenChange: setIsEditModalOpen,\n                    student: selectedStudent || undefined,\n                    onSubmit: handleEditStudent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                    lineNumber: 726,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_students_student_profile_modal__WEBPACK_IMPORTED_MODULE_15__.StudentProfileModal, {\n                    open: isProfileModalOpen,\n                    onOpenChange: setIsProfileModalOpen,\n                    student: selectedStudentProfile\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                    lineNumber: 733,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_students_bulk_actions_modal__WEBPACK_IMPORTED_MODULE_16__.BulkActionsModal, {\n                    open: isBulkActionsModalOpen,\n                    onOpenChange: setIsBulkActionsModalOpen,\n                    selectedStudents: selectedStudents.map((id)=>students.find((s)=>s.id === id)).filter(Boolean),\n                    onComplete: ()=>{\n                        setSelectedStudents([]);\n                        setIsBulkActionsModalOpen(false);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                    lineNumber: 739,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_students_import_export_modal__WEBPACK_IMPORTED_MODULE_17__.ImportExportModal, {\n                    open: isImportExportModalOpen,\n                    onOpenChange: setIsImportExportModalOpen,\n                    students: students,\n                    onImportComplete: (newStudents)=>{\n                        setStudents((prev)=>[\n                                ...prev,\n                                ...newStudents\n                            ]);\n                        setIsImportExportModalOpen(false);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                    lineNumber: 749,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_students_delete_confirmation_dialog__WEBPACK_IMPORTED_MODULE_18__.DeleteConfirmationDialog, {\n                    open: isDeleteDialogOpen,\n                    onOpenChange: setIsDeleteDialogOpen,\n                    student: selectedStudent,\n                    onConfirm: handleDeleteConfirm,\n                    isDeleting: isDeleting\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n                    lineNumber: 759,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n            lineNumber: 353,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\qrsams\\\\app\\\\students\\\\page.tsx\",\n        lineNumber: 352,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"fVT67oiRpYhTsv9y+FafB6/o/CQ=\");\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/students/page.tsx\n"));

/***/ })

});